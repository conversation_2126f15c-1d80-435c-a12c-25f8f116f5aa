from typing import Any,Dict
from agent.graph.state import GraphState
from agent.embedding.read import rag
from langchain_core.messages import HumanMessage

def query_rag(state: GraphState) -> Dict[str, Any]:
    print("---Query Knowledge Base---")
    print(f"DEBUG: Full state: {state}")

    question = state.question
    documents = rag.invoke(question)
    messages = state.messages
    messages.append(HumanMessage(content=question))

    return {
        "question": question,
        "documents": documents,
        "answer": state.answer,
        "messages": messages
    }


