// Next.js API route: 获取ollama模型列表
import type { NextRequest } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export async function GET(req: NextRequest) {
  try {
    // 执行 ollama list 命令
    const { stdout, stderr } = await execAsync('ollama list');
    
    if (stderr) {
      console.error('Ollama list error:', stderr);
      // 如果命令执行失败，返回默认模型
      return new Response(JSON.stringify({ 
        models: ['qwen3:8b'],
        error: 'Failed to fetch models from ollama'
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // 解析ollama list的输出
    const lines = stdout.trim().split('\n');
    const models: string[] = [];
    
    // 跳过标题行，从第二行开始解析
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim();
      if (line) {
        // 提取模型名称（第一列）
        const parts = line.split(/\s+/);
        if (parts.length > 0 && parts[0]) {
          models.push(parts[0]);
        }
      }
    }

    // 如果没有找到模型，返回默认模型
    if (models.length === 0) {
      models.push('qwen3:8b');
    }

    return new Response(JSON.stringify({ 
      models: models,
      success: true
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error fetching ollama models:', error);
    
    // 如果出现错误，返回默认模型
    return new Response(JSON.stringify({ 
      models: ['qwen3:8b'],
      error: 'Failed to execute ollama list command'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
