"use client";
import React, { useState, useEffect, useCallback } from 'react';

interface TypeWriterProps {
  text: string;
  speed?: number; // 基础打字速度（毫秒）
  className?: string;
  onComplete?: () => void;
  allowSkip?: boolean; // 是否允许点击跳过
}

export default function TypeWriter({
  text,
  speed = 30,
  className = '',
  onComplete,
  allowSkip = true
}: TypeWriterProps) {
  const [displayedText, setDisplayedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isSkipped, setIsSkipped] = useState(false);

  useEffect(() => {
    // 重置状态当文本改变时
    setDisplayedText('');
    setCurrentIndex(0);
    setIsSkipped(false);
  }, [text]);

  const handleSkip = useCallback(() => {
    if (allowSkip && currentIndex < text.length) {
      setIsSkipped(true);
      setDisplayedText(text);
      setCurrentIndex(text.length);
    }
  }, [allowSkip, currentIndex, text]);

  const getCharDelay = useCallback((char: string, index: number) => {
    // 为不同字符类型设置不同的延迟，使打字更自然
    if (char === ' ') return speed * 0.5; // 空格快一些
    if (char === '，' || char === '。' || char === '！' || char === '？') return speed * 2; // 标点符号慢一些
    if (char === '\n') return speed * 1.5; // 换行稍慢

    // 添加轻微的随机变化使打字更自然
    const randomFactor = 0.8 + Math.random() * 0.4; // 0.8 到 1.2 的随机因子
    return speed * randomFactor;
  }, [speed]);

  useEffect(() => {
    if (isSkipped) {
      // 如果被跳过，立即完成
      if (onComplete) {
        const timer = setTimeout(() => {
          onComplete();
        }, 100);
        return () => clearTimeout(timer);
      }
      return;
    }

    if (currentIndex < text.length) {
      const char = text[currentIndex];
      const delay = getCharDelay(char, currentIndex);

      const timer = setTimeout(() => {
        setDisplayedText(prev => prev + char);
        setCurrentIndex(prev => prev + 1);
      }, delay);

      return () => clearTimeout(timer);
    } else if (currentIndex === text.length && onComplete) {
      // 打字完成后稍等一下再调用回调，让用户看到完整文本
      const timer = setTimeout(() => {
        onComplete();
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [currentIndex, text, getCharDelay, onComplete, isSkipped]);

  return (
    <span
      className={className}
      onClick={handleSkip}
      style={{
        cursor: allowSkip && currentIndex < text.length ? 'pointer' : 'default',
        userSelect: 'text'
      }}
      title={allowSkip && currentIndex < text.length ? '点击跳过打字动画' : undefined}
    >
      {displayedText}
      {currentIndex < text.length && !isSkipped && (
        <span className="typewriter-cursor">|</span>
      )}
    </span>
  );
}
