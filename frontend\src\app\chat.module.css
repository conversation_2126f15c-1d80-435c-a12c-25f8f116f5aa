
.appContainer {
  display: flex;
  min-height: 100vh;
  background-color: #212121e6;
  font-family: 'Inter', 'Segoe UI', 'Helvetica Neue', <PERSON><PERSON>, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  font-size: 16px;
}

.modelSelectorContainer {
  position: fixed;
  left: 50px; /* 紧靠Sidebar右侧 */
  top: 31px; 
  z-index: 500;
  padding: 8px 12px;
  height: 32px; 
  display: flex;
  align-items: flex-end; /* 使ModelSelector底部对齐 */
}

.mainContent {
  flex: 1;
  margin-left: 50px; /* 为左侧导航栏留出空间 */
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}



.chatContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  flex: 1;
  background: transparent;
  padding-top: 20px;
}

.chatMain {
  width: 100%;
  max-width: 900px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}



@media (max-width: 1000px) {
  .mainContent {
    margin-left: 50px; /* 保持一致的宽度 */
  }

  .modelSelectorContainer {
    left: 50px;
    top: 22px; /* 向下调整，使底部对齐 */
    padding: 6px 10px;
    height: 32px; /* 调整高度以配合新的top位置 */
  }

  .chatMain {
    max-width: 100vw;
    border-radius: 0;
    margin: 0;
  }
  .messages {
    padding-left: 8px;
    padding-right: 8px;
    padding-bottom: 160px;
  }
  .inputArea {
    padding: 40px 8px 40px 8px;
    max-width: 100vw;
    /* 移动端也需要相同的强遮挡效果 */
    box-shadow: 0 -30px 60px 0 #212121, 0 -15px 30px 0 #212121, 0 -5px 15px 0 #212121;
    background: #212121;
  }
}

@media (max-width: 768px) {
  .mainContent {
    margin-left: 45px; /* 移动端使用更窄的导航栏 */
  }

  .modelSelectorContainer {
    left: 45px;
    top: 18px; /* 向下调整，使底部对齐 */
    padding: 4px 8px;
    height: 28px; /* 调整高度以配合新的top位置 */
  }
}

.messages {
  flex: 1;
  padding: 15px 24px 160px 0px; /* 增加底部padding以避免被固定输入框遮挡 */
  display: flex;
  flex-direction: column;
  min-height: auto;
}


.userMessage {
  align-self: flex-end;
  background: #323232d9;
  color: #fff;
  padding: 8px 15px;
  border-radius: 12px;
  max-width: 75%;
  margin: 8px 0;
  font-size: 1.05rem;
  line-height: 1.6;
  word-break: break-word;
  box-shadow: none;
}


.assistantMessage {
  align-self: flex-start;
  background: transparent;
  color: #ececf1;
  padding: 14px 20px;
  border-radius: 12px;
  max-width: 75%;
  margin: 8px 0;
  font-size: 1.05rem;
  line-height: 1.6;
  word-break: break-word;
  box-shadow: none;
}

.inputArea {
  display: flex;
  align-items: center;
  justify-content: center;
  /* padding: 40px 24px 40px 24px; */
  padding-bottom: 40px;
  gap: 0;
  width: 100%;
  max-width: 900px;
  background: #212121;
  pointer-events: auto;
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  /* 添加上边框渐变效果，确保完全遮挡文字 */
  box-shadow: 0 -30px 60px 0 #212121, 0 -15px 30px 0 #212121, 0 -5px 15px 0 #212121;

}



.inputBox {
  flex: 1;
  min-height: 56px;
  max-height: 120px;
  resize: none;
  border: none;
  border-radius: 999px;
  padding: 0 56px 0 56px;
  font-size: 1.15rem;
  outline: none;
  background: #2d2d2f;
  color: #ececf1;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.18);
  width: 100%;
  pointer-events: auto;
  transition: background 0.2s, box-shadow 0.2s;
  line-height: 56px;
  vertical-align: middle;
}


::placeholder {
  color: #888;
  opacity: 1;
  line-height: 56px;
  vertical-align: middle;
}
.inputBox:focus {
  background: #232324;
  box-shadow: 0 4px 16px 0 rgba(0,0,0,0.22);
}


.sendButton {
  background: #19c37d;
  color: #fff;
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  min-width: 44px;
  min-height: 44px;
  max-width: 44px;
  max-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  font-size: 1.35rem;
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.18);
  margin-left: -52px;
  margin-right: 8px;
  pointer-events: auto;
}
.sendButton:hover {
  background: #15a86b;
  box-shadow: 0 4px 16px 0 rgba(0,0,0,0.22);
}

/* 占位符内容样式 */
.placeholderContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 40px 24px;
  text-align: center;
  color: #ececf1;
}

.placeholderContent h2 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 16px;
  color: #ececf1;
}

.placeholderContent p {
  font-size: 1.1rem;
  color: #888;
  line-height: 1.6;
}
