from typing import Any, Dict, cast
from agent.graph.chains.answer_question import get_answer_creator
from agent.graph.state import GraphState
from langchain_core.messages import AIMessage

def answer_question(state: GraphState) -> Dict[str, Any]:
    print("---Answer Question---")

    question = state.question
    documents = state.documents
    messages = state.messages

    # Extract page_content from Document objects to create context string
    context = "\n\n".join([doc.page_content for doc in documents])
    answer_creator = get_answer_creator()
    answer = answer_creator.invoke({"question":question, "context":context})
    response = AIMessage(content=str(answer))
    messages.append(response)

    print(f"DEBUG: Context: {context}")
    print(f"DEBUG: Answer: {answer}")
    print(f"DEBUG: messages: {messages}")

    return {
        "question": question,
        "documents": documents,
        "answer": str(answer),
        "messages": messages
    }

