[tool.poetry]
name = "agent"
version = "0.1.0"
description = ""
authors = ["<PERSON>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.12"
langchain = "0.3.26"
langgraph = "0.4.10"
langchain-community = "0.3.27"
langchain-ollama = "^0.3.10"
langchain-chroma = "^0.2.6"
langsmith = "^0.4.34"
python-dotenv = "^1.1.1"
pymupdf = "^1.26.5"
fastapi = "0.115.12"
uvicorn = "^0.37.0"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
