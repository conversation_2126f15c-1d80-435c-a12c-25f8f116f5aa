.sidebar {
  position: fixed;
  left: 0;
  top: 0;
  width: 50px;
  height: 100vh;
  background: #212121e6;
  border-right: 1px solid #333;
  display: flex;
  flex-direction: column;
  z-index: 1000;
}

.menuItems {
  display: flex;
  flex-direction: column;
  padding: 16px 0;
  gap: 6px;
}

.menuItem {
  width: 38px;
  height: 38px;
  margin: 0 6px;
  background: transparent;
  border: none;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #888;
  position: relative;
}

.menuItem:hover {
  background: #2d2d2f;
  color: #ececf1;
}

.menuItem.active {
  background: #19c37d;
  color: #fff;
}

.menuItem.active:hover {
  background: #15a86b;
}

.icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    width: 45px;
  }

  .menuItem {
    width: 34px;
    height: 34px;
    margin: 0 5px;
  }

  .menuItems {
    padding: 12px 0;
    gap: 4px;
  }

  .icon {
    width: 16px;
    height: 16px;
  }
}
