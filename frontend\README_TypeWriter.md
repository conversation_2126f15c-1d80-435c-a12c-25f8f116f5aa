# TypeWriter 组件使用说明

## 功能特性

✨ **逐字显示效果**：AI 助手的回复会逐字显示，模拟真实的打字效果
🎯 **智能速度调节**：不同字符类型有不同的显示速度，让打字更自然
⚡ **点击跳过**：用户可以点击正在打字的消息来跳过动画
🎨 **光标闪烁**：带有闪烁光标效果，增强视觉体验

## 实现细节

### TypeWriter 组件属性

```typescript
interface TypeWriterProps {
  text: string;           // 要显示的文本
  speed?: number;         // 基础打字速度（毫秒），默认 30ms
  className?: string;     // CSS 类名
  onComplete?: () => void; // 打字完成回调
  allowSkip?: boolean;    // 是否允许点击跳过，默认 true
}
```

### 智能速度调节

- **空格**：速度 × 0.5（更快）
- **标点符号**（，。！？）：速度 × 2（更慢）
- **换行符**：速度 × 1.5（稍慢）
- **普通字符**：添加 0.8-1.2 的随机因子，使打字更自然

### 使用示例

```tsx
<TypeWriter
  text="你好！我是AI助手，有什么可以帮您？"
  speed={30}
  allowSkip={true}
  onComplete={() => console.log('打字完成')}
/>
```

## 样式定制

### 光标样式

光标样式定义在 `globals.css` 中：

```css
@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.typewriter-cursor {
  color: #ececf1;
  animation: blink 1s infinite;
  font-weight: normal;
}
```

### 消息样式

助手消息的样式在 `chat.module.css` 中的 `.assistantMessage` 类中定义。

## 集成到聊天系统

1. **消息状态管理**：每条消息都有 `isTyping` 属性来控制是否显示打字效果
2. **完成回调**：打字完成后会调用 `handleTypingComplete` 来更新消息状态
3. **初始消息**：欢迎消息也会显示打字效果

## 性能优化

- 使用 `useCallback` 优化函数引用
- 合理的 `setTimeout` 清理
- 避免不必要的重渲染
