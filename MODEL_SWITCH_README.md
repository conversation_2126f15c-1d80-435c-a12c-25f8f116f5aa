# 模型切换功能实现说明

## 功能概述

本次实现了在 `handleModelChange` 中的模型动态切换功能。当用户在前端选择不同的AI模型时，系统会自动将选中的模型名称传递到后端，并更新 `llm_factory.py` 中的 `ollama_llm` 实例。

## 实现的功能

### 1. 前端功能
- **模型选择器**: 用户可以通过下拉菜单选择不同的AI模型
- **实时切换**: 选择模型后立即通知后端进行切换
- **状态同步**: 页面加载时自动获取当前使用的模型

### 2. 后端功能
- **动态模型更新**: 支持运行时切换AI模型
- **模型状态管理**: 跟踪当前使用的模型
- **API端点**: 提供专门的模型切换和查询接口

## 技术实现

### 后端修改

#### 1. `llm_factory.py` 增强
```python
def update_ollama_model(model_name: str):
    """动态更新ollama_llm使用的模型"""
    global ollama_llm, llm
    # 创建新的ChatOllama实例并更新全局变量
    
def get_current_model() -> str:
    """获取当前使用的模型名称"""
    return ollama_llm.model
```

#### 2. 新增API端点
- `POST /switch-model`: 专门用于切换模型
- `GET /current-model`: 获取当前使用的模型
- `POST /chat`: 增强支持模型参数

#### 3. 链式组件更新
- 更新 `check_documents.py` 和 `answer_question.py` 以支持动态模型切换
- 确保所有LLM调用都使用最新的模型实例

### 前端修改

#### 1. `ChatPage.tsx` 增强
```typescript
const handleModelChange = async (model: string) => {
  setSelectedModel(model);
  // 调用后端API切换模型
  await fetch('/api/switch-model', {
    method: 'POST',
    body: JSON.stringify({ model: model })
  });
};
```

#### 2. 新增API路由
- `/api/switch-model/route.ts`: 转发模型切换请求
- `/api/current-model/route.ts`: 获取当前模型

## 使用方法

### 1. 启动系统
```bash
# 启动后端
cd agent
python -m rag_agent.agent

# 启动前端
cd frontend
npm run dev
```

### 2. 切换模型
1. 在前端界面点击模型选择器
2. 从下拉列表中选择想要的模型
3. 系统会自动切换到新模型
4. 后续的对话将使用新选择的模型

### 3. 验证切换
- 查看浏览器控制台的日志
- 查看后端终端的切换日志
- 发送测试消息验证模型响应

## 测试

运行测试脚本验证模型切换功能：
```bash
cd agent
python test_model_switch.py
```

## 注意事项

1. **模型可用性**: 确保要切换的模型已在Ollama中安装
2. **性能考虑**: 模型切换可能需要一些时间来加载新模型
3. **错误处理**: 如果模型切换失败，系统会保持使用原有模型
4. **并发安全**: 模型切换是线程安全的

## 故障排除

### 常见问题
1. **模型切换失败**: 检查Ollama是否运行，目标模型是否已安装
2. **前端无响应**: 检查后端API是否正常运行
3. **模型列表为空**: 检查Ollama服务状态和模型安装情况

### 调试方法
- 查看浏览器开发者工具的网络请求
- 查看后端控制台的日志输出
- 使用测试脚本验证后端功能

## 扩展功能

未来可以考虑的增强功能：
1. 模型预热机制
2. 模型性能监控
3. 用户偏好记忆
4. 批量模型管理
