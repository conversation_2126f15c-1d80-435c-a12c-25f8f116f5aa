from agent.graph.graph import wiki_ai

if __name__ == '__main__':
    print("Hello, I'm Wiki Helper. Try to ask me something you're interested in!")

    question = "How many Quotations can be linked to a CCF?"
    question = "What does CCF stand for?"

    print(question)
    # Use config to avoid checkpointer issues
    wiki_answer = wiki_ai.invoke({"question": question}, config={"configurable": {"thread_id": "test"}})
    print(wiki_answer["answer"])
