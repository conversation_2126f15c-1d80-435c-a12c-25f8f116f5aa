import sys
from pathlib import Path
import hashlib

# Add the project root to Python path to enable direct running from the current file
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv
from langchain_chroma import Chroma
from agent.utility.llm_factory import embedding_llm

load_dotenv()


def get_vector_store(persist_directory="../.chroma", collection_name="rag-chroma"):
    """
    Get the Chroma vector store instance.

    :param persist_directory: Directory where the vector database is persisted
    :param collection_name: Name of the collection
    :return: Chroma vector store instance
    """
    return Chroma(
        collection_name=collection_name,
        persist_directory=persist_directory,
        embedding_function=embedding_llm
    )


def delete_all_embeddings(persist_directory="../.chroma", collection_name="rag-chroma"):
    """
    Delete all embeddings from the vector database.

    :param persist_directory: Directory where the vector database is persisted
    :param collection_name: Name of the collection
    :return: Number of deleted documents
    """
    try:
        vector_store = get_vector_store(persist_directory, collection_name)

        # Get all documents in the collection
        collection = vector_store._collection
        all_data = collection.get()

        if not all_data['ids']:
            print("No documents found in the collection.")
            return 0

        total_docs = len(all_data['ids'])

        # Delete all documents
        collection.delete(ids=all_data['ids'])

        print(f"Successfully deleted all {total_docs} documents from the collection '{collection_name}'.")
        return total_docs

    except Exception as e:
        print(f"Error deleting all embeddings: {e}")
        return 0


def find_duplicate_embeddings(persist_directory="../.chroma", collection_name="rag-chroma"):
    """
    Find duplicate embeddings based on document content hash.

    :param persist_directory: Directory where the vector database is persisted
    :param collection_name: Name of the collection
    :return: Dictionary mapping content hash to list of document IDs
    """
    try:
        vector_store = get_vector_store(persist_directory, collection_name)
        collection = vector_store._collection

        # Get all documents
        all_data = collection.get(include=['documents', 'metadatas'])

        if not all_data['ids']:
            print("No documents found in the collection.")
            return {}

        # Group documents by content hash
        content_hash_to_ids = {}

        for doc_id, document, metadata in zip(
            all_data['ids'],
            all_data['documents'],
            all_data['metadatas'] or [{}] * len(all_data['ids'])
        ):
            # Create a hash based on document content
            content_hash = hashlib.md5(document.encode('utf-8')).hexdigest()

            if content_hash not in content_hash_to_ids:
                content_hash_to_ids[content_hash] = []

            content_hash_to_ids[content_hash].append({
                'id': doc_id,
                'content': document,
                'metadata': metadata
            })

        # Filter to only duplicates (more than one document with same hash)
        duplicates = {
            hash_val: docs for hash_val, docs in content_hash_to_ids.items()
            if len(docs) > 1
        }

        return duplicates

    except Exception as e:
        print(f"Error finding duplicate embeddings: {e}")
        return {}


def delete_duplicate_embeddings(persist_directory="../.chroma", collection_name="rag-chroma", keep_first=True):
    """
    Delete duplicate embeddings, keeping only one copy of each unique content.

    :param persist_directory: Directory where the vector database is persisted
    :param collection_name: Name of the collection
    :param keep_first: If True, keep the first occurrence; if False, keep the last
    :return: Number of deleted documents
    """
    try:
        duplicates = find_duplicate_embeddings(persist_directory, collection_name)

        if not duplicates:
            print("No duplicate embeddings found.")
            return 0

        vector_store = get_vector_store(persist_directory, collection_name)
        collection = vector_store._collection

        ids_to_delete = []
        total_duplicates = 0

        for content_hash, docs in duplicates.items():
            total_duplicates += len(docs)
            print(f"Found {len(docs)} duplicates for content hash: {content_hash[:8]}...")

            # Sort by ID to ensure consistent behavior
            docs.sort(key=lambda x: x['id'])

            # Keep first or last, delete the rest
            if keep_first:
                docs_to_delete = docs[1:]  # Keep first, delete rest
            else:
                docs_to_delete = docs[:-1]  # Keep last, delete rest

            for doc in docs_to_delete:
                ids_to_delete.append(doc['id'])
                print(f"  Marking for deletion: {doc['id']}")

        if ids_to_delete:
            # Delete the duplicate documents
            collection.delete(ids=ids_to_delete)
            deleted_count = len(ids_to_delete)
            kept_count = total_duplicates - deleted_count

            print(f"\nSuccessfully deleted {deleted_count} duplicate documents.")
            print(f"Kept {kept_count} unique documents.")
            return deleted_count
        else:
            print("No documents to delete.")
            return 0

    except Exception as e:
        print(f"Error deleting duplicate embeddings: {e}")
        return 0


def show_collection_stats(persist_directory="../.chroma", collection_name="rag-chroma"):
    """
    Show statistics about the collection.

    :param persist_directory: Directory where the vector database is persisted
    :param collection_name: Name of the collection
    """
    try:
        vector_store = get_vector_store(persist_directory, collection_name)
        collection = vector_store._collection

        all_data = collection.get()
        total_docs = len(all_data['ids']) if all_data['ids'] else 0

        print(f"\n=== Collection Statistics ===")
        print(f"Collection name: {collection_name}")
        print(f"Persist directory: {persist_directory}")
        print(f"Total documents: {total_docs}")

        if total_docs > 0:
            # Find duplicates for statistics
            duplicates = find_duplicate_embeddings(persist_directory, collection_name)
            duplicate_groups = len(duplicates)
            total_duplicate_docs = sum(len(docs) for docs in duplicates.values())
            unique_docs = total_docs - total_duplicate_docs + duplicate_groups

            print(f"Unique documents: {unique_docs}")
            print(f"Duplicate groups: {duplicate_groups}")
            print(f"Total duplicate documents: {total_duplicate_docs}")

        print("=" * 30)

    except Exception as e:
        print(f"Error getting collection statistics: {e}")


if __name__ == "__main__":
    # Example usage
    print("=== Chroma Vector Database Management ===\n")

    # Show current statistics
    show_collection_stats()

    # Uncomment the operation you want to perform:

    # 1. Delete duplicate embeddings (keep first occurrence)
    # print("\n--- Deleting Duplicate Embeddings ---")
    # deleted_count = delete_duplicate_embeddings(keep_first=True)

    # 2. Delete all embeddings
    # print("\n--- Deleting All Embeddings ---")
    # deleted_count = delete_all_embeddings()

    # Show statistics after operation
    # show_collection_stats()