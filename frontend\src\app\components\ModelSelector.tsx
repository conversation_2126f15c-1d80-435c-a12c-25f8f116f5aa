"use client";
import React, { useState, useEffect, useRef } from 'react';
import styles from './ModelSelector.module.css';

interface ModelSelectorProps {
  selectedModel: string;
  onModelChange: (model: string) => void;
}



const ModelSelector: React.FC<ModelSelectorProps> = ({ selectedModel, onModelChange }) => {
  const [models, setModels] = useState<string[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [hoveredModel, setHoveredModel] = useState<string | null>(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const dropdownRef = useRef<HTMLDivElement>(null);

  // 将模型名称首字母大写
  const capitalizeModelName = (name: string) => {
    return name.charAt(0).toUpperCase() + name.slice(1);
  };

  // 处理鼠标进入事件，计算tooltip位置
  const handleMouseEnter = (model: string, event: React.MouseEvent) => {
    const rect = event.currentTarget.getBoundingClientRect();
    setTooltipPosition({
      x: rect.right + 10, // 显示在右侧
      y: rect.top + rect.height / 2
    });
    setHoveredModel(model);
  };

  // 处理鼠标离开事件
  const handleMouseLeave = () => {
    setHoveredModel(null);
  };

  useEffect(() => {
    fetchModels();
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const fetchModels = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/models');
      if (response.ok) {
        const data = await response.json();
        // 过滤掉含有"embed"的模型，并按字母顺序排列
        const filteredModels = (data.models || [])
          .filter((model: string) => !model.toLowerCase().includes('embed'))
          .sort((a: string, b: string) => a.localeCompare(b));
        setModels(filteredModels);
      } else {
        // 如果API不可用，使用默认模型
        setModels(['qwen3:8b']);
      }
    } catch (error) {
      console.error('Failed to fetch models:', error);
      // 如果获取失败，使用默认模型
      setModels(['qwen3:8b']);
    } finally {
      setLoading(false);
    }
  };

  const handleModelSelect = (model: string) => {
    onModelChange(model);
    setIsOpen(false);
  };

  const displayName = selectedModel || 'qwen3:8b';

  return (
    <div className={styles.modelSelector} ref={dropdownRef}>
      <button
        className={styles.selectorButton}
        onClick={() => setIsOpen(!isOpen)}
        disabled={loading}
      >
        <span className={styles.modelName}>
          {loading ? '加载中...' : capitalizeModelName(displayName)}
        </span>
        <svg
          className={`${styles.chevron} ${isOpen ? styles.chevronUp : ''}`}
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="currentColor"
        >
          <path d="M7 10l5 5 5-5z"/>
        </svg>
      </button>

      {isOpen && !loading && (
        <div className={styles.dropdown}>
          <div className={styles.dropdownHeader}>
            <span>选择AI模型</span>
          </div>
          <div className={styles.modelList}>
            {models.map((model) => (
              <div
                key={model}
                className={styles.modelItemTooltip}
                onMouseEnter={(e) => handleMouseEnter(model, e)}
                onMouseLeave={handleMouseLeave}
              >
                <button
                  className={`${styles.modelItem} ${selectedModel === model ? styles.selected : ''}`}
                  onClick={() => handleModelSelect(model)}
                >
                  <span className={styles.modelItemName}>{capitalizeModelName(model)}</span>
                  {selectedModel === model && (
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                    </svg>
                  )}
                </button>
              </div>
            ))}
          </div>

          {/* 全局tooltip */}
          {hoveredModel && (
            <div
              className={`${styles.modelItemTooltipText} ${styles.visible}`}
              style={{
                left: `${tooltipPosition.x}px`,
                top: `${tooltipPosition.y}px`,
                transform: 'translate(0, -50%)'
              }}
            >
              {capitalizeModelName(hoveredModel)}
              <div className={styles.tooltipArrow}></div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ModelSelector;
