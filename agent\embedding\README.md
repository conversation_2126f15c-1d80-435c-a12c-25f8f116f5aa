# Embedding Management

This directory contains modules for managing embeddings in the Chroma vector database.

## Files

- `create.py` - Create and ingest embeddings from documents
- `read.py` - Read and search embeddings
- `update.py` - Update existing embeddings
- `delete.py` - Delete embeddings (duplicates and all)

## Delete Functionality

The `delete.py` module provides two main functions for managing embeddings:

### 1. Delete Duplicate Embeddings

Removes duplicate embeddings based on document content hash, keeping only one copy of each unique content.

```python
from agent.embedding.delete import delete_duplicate_embeddings

# Delete duplicates, keep first occurrence
deleted_count = delete_duplicate_embeddings(keep_first=True)

# Delete duplicates, keep last occurrence  
deleted_count = delete_duplicate_embeddings(keep_first=False)
```

### 2. Delete All Embeddings

Removes all embeddings from the vector database.

```python
from agent.embedding.delete import delete_all_embeddings

# Delete all embeddings
deleted_count = delete_all_embeddings()
```

### 3. Find Duplicates (without deleting)

Find duplicate embeddings without deleting them.

```python
from agent.embedding.delete import find_duplicate_embeddings

# Find duplicates
duplicates = find_duplicate_embeddings()
print(f"Found {len(duplicates)} groups of duplicates")
```

### 4. Show Collection Statistics

Display statistics about the collection.

```python
from agent.embedding.delete import show_collection_stats

# Show collection stats
show_collection_stats()
```

## Usage Examples

### Interactive Usage

Run the test script for interactive management:

```bash
python -m agent.utility.embedding_delete_test
```

### Programmatic Usage

```python
from agent.embedding.delete import (
    delete_duplicate_embeddings,
    delete_all_embeddings,
    show_collection_stats
)

# Show current stats
show_collection_stats()

# Delete duplicates
deleted = delete_duplicate_embeddings()
print(f"Deleted {deleted} duplicate documents")

# Show final stats
show_collection_stats()
```

## Parameters

All functions accept these optional parameters:

- `persist_directory` (str): Directory where the vector database is persisted (default: "../.chroma")
- `collection_name` (str): Name of the collection (default: "rag-chroma")

## Notes

- Duplicate detection is based on MD5 hash of document content
- The functions are safe and include error handling
- Always backup your data before performing delete operations
- Use `show_collection_stats()` to verify results after operations
