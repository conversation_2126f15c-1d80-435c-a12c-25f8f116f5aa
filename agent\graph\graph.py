from dotenv import load_dotenv
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import StateGraph, END

from agent.utility.consts import QUERY_RAG, CHECK_DOCUMENTS, ANSWER_QUESTION
from agent.graph.nodes import query_rag,answer_question, check_documents
from agent.graph.state import GraphState

load_dotenv()

workflow = StateGraph(GraphState)
workflow.add_node(QUERY_RAG, query_rag)
workflow.add_node(CHECK_DOCUMENTS, check_documents)
workflow.add_node(ANSWER_QUESTION, answer_question)

workflow.set_entry_point(QUERY_RAG)

workflow.add_edge(QUERY_RAG, CHECK_DOCUMENTS)
workflow.add_edge(CHECK_DOCUMENTS, ANSWER_QUESTION)
workflow.add_edge(ANSWER_QUESTION, END)

# checkpointer = MemorySaver()
# wiki_ai = workflow.compile(checkpointer=checkpointer)
wiki_ai = workflow.compile()
