from fastapi import Request
from pydantic import BaseModel
import os
from dotenv import load_dotenv
from fastapi import FastAPI
import uvicorn
from fastapi.middleware.cors import CORSMiddleware
from agent.graph.graph import wiki_ai
from agent.graph.state import GraphState
from langchain_core.messages import HumanMessage, AIMessage
from agent.utility.llm_factory import update_ollama_model, get_current_model

class ChatRequest(BaseModel):
    question: str
    messages: list = []
    model: str = "qwen3:8b" 

class ChatResponse(BaseModel):
    answer: str
    messages: list

class ModelSwitchRequest(BaseModel):
    model: str

class ModelSwitchResponse(BaseModel):
    success: bool
    message: str
    current_model: str

class CurrentModelResponse(BaseModel):
    current_model: str

load_dotenv()

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_methods=["*"],
    allow_headers=["*"],
    allow_credentials=True
)

@app.post("/chat", response_model=ChatResponse)
async def chat_endpoint(chat: ChatRequest):

    current_model = get_current_model()
    if chat.model and chat.model != current_model:
        print(f"Switching model from {current_model} to {chat.model}")
        update_ollama_model(chat.model)

    # Convert frontend message format to LangChain message format
    langchain_messages = []
    for msg in chat.messages or []:
        if msg.get("type") == "human":
            langchain_messages.append(HumanMessage(content=msg["content"]))
        elif msg.get("type") == "ai":
            langchain_messages.append(AIMessage(content=msg["content"]))

    state = GraphState(
        question=chat.question,
        documents=[],
        answer="",
        messages=langchain_messages
    )
    result = await wiki_ai.ainvoke(state.model_dump())

    # Convert LangChain messages back to frontend format
    frontend_messages = []
    for msg in result["messages"]:
        if hasattr(msg, 'content'):
            msg_type = "human" if msg.__class__.__name__ == "HumanMessage" else "ai"
            frontend_messages.append({"type": msg_type, "content": msg.content})

    return ChatResponse(answer=result["answer"], messages=frontend_messages)

@app.post("/switch-model", response_model=ModelSwitchResponse)
async def switch_model_endpoint(request: ModelSwitchRequest):
    """Switch the AI model."""
    try:
        current_model = get_current_model()
        if request.model != current_model:
            print(f"Switching model from {current_model} to {request.model}")
            update_ollama_model(request.model)
            new_current_model = get_current_model()
            return ModelSwitchResponse(
                success=True,
                message=f"AI model has been switched to: {request.model}",
                current_model=new_current_model
            )
        else:
            return ModelSwitchResponse(
                success=True,
                message=f"AI model is already set to: {request.model}",
                current_model=current_model
            )
    except Exception as e:
        return ModelSwitchResponse(
            success=False,
            message=f"AI model switch failed: {str(e)}",
            current_model=get_current_model()
        )

@app.get("/current-model", response_model=CurrentModelResponse)
async def get_current_model_endpoint():
    current_model = get_current_model()
    return CurrentModelResponse(current_model=current_model)

@app.get("/")
def root():
    return {"status": "ok"}

@app.get("/health")
def health():
    """Health check."""
    return {"status": "ok"}

def main():
    port = int(os.getenv("PORT", "8123"))
    uvicorn.run(
        "rag_agent.agent:app",
        host="0.0.0.0",
        port=port,
        reload=True
    )



