#!/usr/bin/env python3
"""
测试模型切换功能的脚本
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from agent.utility.llm_factory import update_ollama_model, get_current_model

def test_model_switch():
    """测试模型切换功能"""
    print("=== 测试模型切换功能 ===")
    
    # 获取当前模型
    current_model = get_current_model()
    print(f"当前模型: {current_model}")
    
    # 测试切换到不同的模型
    test_models = ["qwen3:8b", "llama3.2:3b", "qwen3:14b"]
    
    for model in test_models:
        print(f"\n--- 尝试切换到模型: {model} ---")
        try:
            result = update_ollama_model(model)
            new_current_model = get_current_model()
            print(f"切换后的模型: {new_current_model}")
            
            if new_current_model == model:
                print("✅ 模型切换成功!")
            else:
                print("❌ 模型切换失败!")
                
        except Exception as e:
            print(f"❌ 模型切换出错: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_model_switch()
