import os
import sys
from pathlib import Path

# Add the project root to Python path to enable direct running from the current file
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders.text import TextLoader
from langchain_community.document_loaders.pdf import PDFMinerLoader, PyMuPDFLoader
from langchain_community.document_loaders.unstructured import UnstructuredFileLoader
from langchain_chroma import Chroma
from agent.utility.llm_factory import embedding_llm

load_dotenv()

def embed_documents(path, persist_directory="../.chroma"):
    """
    Ingest a single file or all files in a directory into the vector database.
    :param path: File path or directory path
    :param persist_directory: Directory to persist the vector database
    """
    file_list = []
    if os.path.isdir(path):
        # Walk through the directory and collect all files
        for root, _, files in os.walk(path):
            for file in files:
                file_list.append(os.path.join(root, file))
    elif os.path.isfile(path):
        file_list = [path]
    else:
        print(f"Path does not exist: {path}")
        return

    all_docs = []
    for file_path in file_list:
        # Select the appropriate loader based on file extension
        if file_path.endswith(".txt"):
            loader = TextLoader(file_path)
        elif file_path.endswith(".pdf"):
            try:
                loader = PyMuPDFLoader(file_path)
            except Exception:
                loader = PDFMinerLoader(file_path)
        else:
            loader = UnstructuredFileLoader(file_path)
        try:
            documents = loader.load()
            text_splitter = RecursiveCharacterTextSplitter(chunk_size=200, chunk_overlap=10)
            docs = text_splitter.split_documents(documents)
            all_docs.extend(docs)
            print(f"Processed file: {file_path}, split into {len(docs)} chunks")
        except Exception as e:
            print(f"Failed to process file: {file_path}, error: {e}")

    if not all_docs:
        print("No documents were processed.")
        return

    # Create embeddings and store in Chroma
    vectordb = Chroma.from_documents(
        documents=all_docs,
        collection_name="rag-chroma",
        embedding=embedding_llm,
        persist_directory=persist_directory
    )

    print(f"Ingested {len(all_docs)} chunks into vector DB at '{persist_directory}'.")

if __name__ == "__main__":
    embed_documents(r"D:\AI\LangChain\QDocs")
