import type { NextRequest } from 'next/server';

export async function POST(req: NextRequest) {
	const body = await req.json();
	const resp = await fetch('http://localhost:8123/switch-model', {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
		},
		body: JSON.stringify(body),
	});
	const data = await resp.json();
	return new Response(JSON.stringify(data), {
		status: resp.status,
		headers: { 'Content-Type': 'application/json' },
	});
}
