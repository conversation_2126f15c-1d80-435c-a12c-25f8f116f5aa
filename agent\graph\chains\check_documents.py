from langchain_core.prompts import ChatPromptTemplate
from pydantic import BaseModel,Field

class GradeDocuments(BaseModel):
    """Binary score for relevance check on retrieved documents."""
    binary_score: str = Field(description="Documents are relevant to the question, 'yes' or 'no'")


system_prompt = """You are a grader assessing relevance of a retrieved document to a user question. \n 
    If the document contains keyword(s) or semantic meaning related to the question, grade it as relevant. \n
    Give a binary score 'yes' or 'no' score to indicate whether the document is relevant to the question."""

grade_prompt = ChatPromptTemplate.from_messages(
    [
        ("system", system_prompt),
        ("human","User question: \n\n {question} \n\n Retrieved document: \n\n {document}")
    ]
)

def get_structured_llm_grader():
    from agent.utility.llm_factory import llm
    return llm.with_structured_output(GradeDocuments)

def get_document_grader():
    return grade_prompt | get_structured_llm_grader()


