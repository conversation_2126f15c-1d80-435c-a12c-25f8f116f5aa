from dotenv import load_dotenv
from langchain_ollama import ChatOllama
from langchain_ollama import OllamaEmbeddings

load_dotenv()

default_model = "qwen3:8b"

ollama_llm = ChatOllama(model= default_model, temperature=0, reasoning=True)

ollama_embedding_llm = OllamaEmbeddings(model = "qwen3_embedding:latest")

llm = ollama_llm

embedding_llm = ollama_embedding_llm

def update_ollama_model(model_name: str):
    global ollama_llm, llm
    try:
        new_ollama_llm = ChatOllama(model=model_name, temperature=0, reasoning=True)

        ollama_llm = new_ollama_llm
        llm = new_ollama_llm

        print(f"Successfully updated ollama_llm to use model: {model_name}")
        return new_ollama_llm
    except Exception as e:
        print(f"Error updating ollama_llm to model {model_name}: {e}")
        return ollama_llm

def get_current_model() -> str:
    return ollama_llm.model