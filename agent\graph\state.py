from typing import List, Optional
from pydantic import BaseModel
from langchain_core.messages import BaseMessage
from langchain_core.documents import Document

class GraphState(BaseModel):
    """
    Represents the state of our graph.

    Attributes:
        question: User question
        documents: List of relevant documents
        answer: The answer of LLM
        messages: List of BaseMessage objects (HumanMessage, AIMessage, etc.)
    """
    question: str = ""
    documents: List[Document] = []
    answer: str = ""
    messages: List[BaseMessage] = []
