from typing import Any,Dict
from agent.graph.chains.check_documents import get_document_grader
from agent.graph.state import GraphState

def check_documents(state: GraphState) -> Dict[str, Any]:
    """
    Determines whether the retrieved documents are relevant to the question
    If any document is not relevant, we will filter out the irrelevant documents

    Args:
        state (dict): The current graph state

    Returns:
        state (dict): Filtered out irrelevant documents
    """

    print("---Check If Retrieved Documents Are Relevant To Question---")

    question = state.question
    documents = state.documents
    filtered_documents = []

    for document in documents:
        document_grader = get_document_grader()
        score = document_grader.invoke({"question": question, "document": document.page_content})
        grade = score.binary_score
        print(f"DEBUG: Check Document : {document.page_content}" )

        if grade.lower() == "yes":
            print(f"DEBUG:---Check Result: Document is relevant---")
            filtered_documents.append(document)
        else:
            print(f"DEBUG:---Check Result: Document is NOT relevant---")

    return {
        "question": question,
        "documents": filtered_documents,
        "answer": state.answer,
        "messages": state.messages
    }
