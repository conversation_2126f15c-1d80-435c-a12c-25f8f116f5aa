import type { NextRequest } from 'next/server';

export async function GET(req: NextRequest) {
	try {
		const resp = await fetch('http://localhost:8123/current-model', {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
			},
		});
		const data = await resp.json();
		return new Response(JSON.stringify(data), {
			status: resp.status,
			headers: { 'Content-Type': 'application/json' },
		});
	} catch (error) {
		console.error('Error fetching current model:', error);
		return new Response(JSON.stringify({ 
			current_model: 'qwen3:8b'
		}), {
			status: 200,
			headers: { 'Content-Type': 'application/json' },
		});
	}
}
