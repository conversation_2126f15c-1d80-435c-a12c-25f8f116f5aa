@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  margin: 0;
  padding: 0;
  /* 确保页面可以正常滚动 */
  overflow-x: hidden;
  overflow-y: auto;
}

html {
  /* 确保滚动行为平滑 */
  scroll-behavior: smooth;
}

/* TypeWriter 光标动画 */
@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

.typewriter-cursor {
  color: #ececf1;
  animation: blink 1s infinite;
  font-weight: normal;
}
