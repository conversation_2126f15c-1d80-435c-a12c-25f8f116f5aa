.modelSelector {
  position: relative;
  display: inline-block;
}

.selectorButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #212121e6;
  border: none;
  border-radius: 8px;
  color: #ececf1;
  font-size: 18px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;
  justify-content: space-between;
}

.selectorButton:hover {
  background: #2d2d2f;
}

.selectorButton:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.modelName {
  flex: 1;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chevron {
  transition: transform 0.2s ease;
  color: #888;
}

.chevronUp {
  transform: rotate(180deg);
}

.dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 4px;
  background: #212121e6;
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  overflow: visible;
}

.dropdownHeader {
  padding: 12px 16px;
  border-bottom: 1px solid #333;
  font-size: 12px;
  font-weight: 600;
  color: #888;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.modelList {
  max-height: 200px;
  overflow-y: auto;
}

.modelItem {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: transparent;
  border: none;
  color: #ececf1;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.2s ease;
  text-align: left;
}

.modelItem:hover {
  background: #2d2d2f;
}

.modelItem.selected {
  background: #19c37d;
  color: #fff;
}

.modelItem.selected:hover {
  background: #15a86b;
}

.modelItemName {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 滚动条样式 */
.modelList::-webkit-scrollbar {
  width: 6px;
}

.modelList::-webkit-scrollbar-track {
  background: #212121e6;
}

.modelList::-webkit-scrollbar-thumb {
  background: #333;
  border-radius: 3px;
}

.modelList::-webkit-scrollbar-thumb:hover {
  background: #444;
}



/* 下拉列表项的 tooltip */
.modelItemTooltip {
  position: relative;
  width: 100%;
}

.modelItemTooltipText {
  visibility: hidden;
  width: auto;
  min-width: 150px;
  max-width: 300px;
  background-color: #1a1a1a;
  color: #ececf1;
  text-align: left;
  border-radius: 6px;
  padding: 6px 10px;
  position: fixed;
  z-index: 9999;
  opacity: 0;
  transition: opacity 0.2s ease;
  font-size: 12px;
  font-weight: 400;
  white-space: nowrap;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  border: 1px solid #333;
  pointer-events: none;
}

.tooltipArrow {
  position: absolute;
  top: 50%;
  left: -5px;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-right: 5px solid #1a1a1a;
}

.modelItemTooltipText.visible {
  visibility: visible;
  opacity: 1;
}

.modelItemTooltip:hover .modelItemTooltipText {
  visibility: visible;
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .selectorButton {
    min-width: 120px;
    font-size: 13px;
    padding: 6px 10px;
  }

  .dropdown {
    left: -20px;
    right: -20px;
  }

  /* 移动端调整 tooltip 位置 */
  .modelItemTooltipText {
    right: -200px;
    margin-right: 0;
  }

  .modelItemTooltipText::after {
    left: auto;
    right: 100%;
    border-color: transparent #1a1a1a transparent transparent;
  }
}
