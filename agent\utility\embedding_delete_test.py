#!/usr/bin/env python3
"""
Test script for the delete.py functionality.
This script demonstrates how to use the delete functions.
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from agent.embedding.delete import (
    delete_all_embeddings,
    delete_duplicate_embeddings,
    find_duplicate_embeddings,
    show_collection_stats
)

def main():
    print("=== Testing Delete Functionality ===\n")
    
    # Show current collection statistics
    print("1. Current collection statistics:")
    show_collection_stats()
    
    # Find duplicates without deleting
    print("\n2. Finding duplicate embeddings:")
    duplicates = find_duplicate_embeddings()
    
    if duplicates:
        print(f"Found {len(duplicates)} groups of duplicate content:")
        for i, (content_hash, docs) in enumerate(duplicates.items(), 1):
            print(f"  Group {i}: {len(docs)} documents with hash {content_hash[:8]}...")
            for doc in docs:
                print(f"    - ID: {doc['id']}")
                print(f"      Content preview: {doc['content'][:100]}...")
    else:
        print("No duplicates found.")
    
    # Ask user what they want to do
    print("\n3. Available operations:")
    print("   a) Delete duplicate embeddings (keep first)")
    print("   b) Delete duplicate embeddings (keep last)")
    print("   c) Delete ALL embeddings")
    print("   d) Just show statistics and exit")
    
    choice = input("\nEnter your choice (a/b/c/d): ").lower().strip()
    
    if choice == 'a':
        print("\n--- Deleting duplicate embeddings (keeping first occurrence) ---")
        deleted_count = delete_duplicate_embeddings(keep_first=True)
        print(f"Operation completed. Deleted {deleted_count} documents.")
        
    elif choice == 'b':
        print("\n--- Deleting duplicate embeddings (keeping last occurrence) ---")
        deleted_count = delete_duplicate_embeddings(keep_first=False)
        print(f"Operation completed. Deleted {deleted_count} documents.")
        
    elif choice == 'c':
        confirm = input("Are you sure you want to delete ALL embeddings? (yes/no): ").lower().strip()
        if confirm == 'yes':
            print("\n--- Deleting ALL embeddings ---")
            deleted_count = delete_all_embeddings()
            print(f"Operation completed. Deleted {deleted_count} documents.")
        else:
            print("Operation cancelled.")
            
    elif choice == 'd':
        print("Exiting without making changes.")
        
    else:
        print("Invalid choice. Exiting.")
        return
    
    # Show final statistics
    print("\n4. Final collection statistics:")
    show_collection_stats()

if __name__ == "__main__":
    main()
