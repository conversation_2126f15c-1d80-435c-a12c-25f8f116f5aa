from dotenv import load_dotenv
from langchain_chroma import Chroma
from agent.utility.llm_factory import embedding_llm

load_dotenv()

rag = Chroma(
    collection_name="rag-chroma",
    persist_directory="../.chroma",
    embedding_function=embedding_llm).as_retriever()


if __name__ == '__main__':
    vector_store = Chroma(
    collection_name="rag-chroma",
    persist_directory="../.chroma",
    embedding_function=embedding_llm)

    results = vector_store.similarity_search(
        query="CCF",
        k=10
    )

    for doc in results:
        print(f"文档内容: {doc.page_content}")
        print(f"元数据: {doc.metadata}")
        print("-" * 50)
